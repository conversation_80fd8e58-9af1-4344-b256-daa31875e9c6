# Mastra Dictionary Mobile App

Bu mobil uy<PERSON>, Mastra Agent Framework kullanarak AI destekli kelime tanımları sağlayan React Native/Expo uygulamasıdır.

## Özellikler

- 🤖 AI destekli kelime tanımları
- 📱 React Native ile mobil deneyim
- 🔍 Gerçek zamanlı arama
- 🎨 Modern ve kullanıcı dostu arayüz
- 🌐 Mastra MCP tools entegrasyonu

## Kurulum

1. Bağımlılıkları yükleyin:
```bash
npm install
```

2. Uygulamayı başlatın:
```bash
npm start
```

## Expo Go ile Test Etme

1. Telefonunuza Expo Go uygulamasını indirin
2. Bilgisayarınız ve telefonunuz aynı WiFi ağında olduğundan emin olun
3. `npm start` komutu ile uygulamayı başlatın
4. QR kodu Expo Go ile tarayın

## Önemli Notlar

- API URL'si bilgisayarınızın IP adresine göre ayarlanmıştır (*************:3003)
- Mastra server'ının çalışıyor olması gerekir
- Telefonunuz ve bilgisayarınız aynı ağda olmalıdır

## Kullanım

1. Kelime giriş alanına tanımını öğrenmek istediğiniz kelimeyi yazın
2. "Search" butonuna basın veya klavyede Enter'a basın
3. AI agent kelimeyi analiz edip tanımını getirecektir
4. "Clear" butonu ile arama sonuçlarını temizleyebilirsiniz

## Teknolojiler

- React Native
- Expo
- Mastra Agent Framework
- MCP (Model Context Protocol) Tools
