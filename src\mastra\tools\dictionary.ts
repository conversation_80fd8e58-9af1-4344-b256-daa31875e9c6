import { createTool } from '@mastra/core';
import { z } from 'zod';

export const getDefinitions = createTool({
  id: 'get_definitions',
  description: 'Get definitions for a word using local dictionary database',
  inputSchema: z.object({
    word: z.string().describe('The word to get definitions for'),
  }),
  execute: async (context: any) => {
    console.log('🔧 Tool context:', context);
    const word = context.context?.word || context.word || context.input?.word || 'test';

    try {
      console.log(`🔍 Dictionary tool çağrılıyor: get_definitions("${word}")`);

      // Önce gerçek API'yi dene (Free Dictionary API)
      try {
        console.log('🌐 Free Dictionary API deneniyor...');
        const response = await fetch(`https://api.dictionaryapi.dev/api/v2/entries/en/${encodeURIComponent(word)}`);

        if (response.ok) {
          const data = await response.json();
          if (data && data.length > 0) {
            const definitions: string[] = [];

            data[0].meanings?.forEach((meaning: any) => {
              meaning.definitions?.forEach((def: any) => {
                if (def.definition) {
                  definitions.push(def.definition);
                }
              });
            });

            if (definitions.length > 0) {
              console.log(`✅ "${word}" için ${definitions.length} tanım API'den alındı`);
              return {
                success: true,
                word: word,
                definitions: definitions.slice(0, 5), // İlk 5 tanımı al
                message: `Definitions for "${word}" retrieved from Dictionary API.`,
                source: 'Free Dictionary API'
              };
            }
          }
        }
      } catch (apiError) {
        console.log('⚠️ API çağrısı başarısız, yerel veriye geçiliyor...');
      }

      console.log('📚 Yerel sözlük verisi kullanılıyor...');

      const mockDefinitions: Record<string, string[]> = {
        'example': [
          'A thing characteristic of its kind or illustrating a general rule',
          'A person or thing regarded in terms of their fitness to be imitated or the likelihood of their being imitated',
          'An instance serving for illustration'
        ],
        'computer': [
          'An electronic device for storing and processing data, typically in binary form, according to instructions given to it in a variable program',
          'A person who makes calculations, especially with a calculating machine'
        ],
        'hello': [
          'Used as a greeting or to begin a phone conversation',
          'An exclamation of surprise'
        ],
        'test': [
          'A procedure intended to establish the quality, performance, or reliability of something',
          'An examination of knowledge or ability'
        ],
        'water': [
          'A colorless, transparent, odorless liquid that forms the seas, lakes, rivers, and rain',
          'The liquid that descends from the clouds as rain, forms streams, lakes, and seas',
          'A liquid without taste or smell that is necessary for most animal and plant life'
        ],
        'artificial': [
          'Made or produced by human beings rather than occurring naturally',
          'Not genuine; imitation or synthetic',
          'Created by humans to replace something natural'
        ],
        'intelligence': [
          'The ability to acquire and apply knowledge and skills',
          'The collection of information of military or political value',
          'Mental capacity; the ability to think, reason, and understand'
        ],
        'technology': [
          'The application of scientific knowledge for practical purposes',
          'Machinery and equipment developed from the application of scientific knowledge',
          'The branch of knowledge dealing with engineering or applied sciences'
        ],
        'science': [
          'The intellectual and practical activity encompassing the systematic study of the structure and behavior of the physical and natural world',
          'A particular area of scientific study',
          'Knowledge about or study of the natural world based on facts learned through experiments and observation'
        ],
        'language': [
          'The method of human communication, either spoken or written',
          'The system of communication used by a particular community or country',
          'A system of symbols and rules for writing programs or algorithms'
        ],
        'learning': [
          'The acquisition of knowledge or skills through experience, study, or by being taught',
          'The process of gaining knowledge',
          'Knowledge acquired through education or experience'
        ],
        'knowledge': [
          'Facts, information, and skills acquired by a person through experience or education',
          'The theoretical or practical understanding of a subject',
          'Awareness or familiarity gained by experience of a fact or situation'
        ],
        'programming': [
          'The process of creating a set of instructions that tell a computer how to perform a task',
          'The activity of writing computer programs',
          'The action or process of writing computer programs'
        ],
        'software': [
          'Computer programs and other operating information used by a computer',
          'The programs and other operating information used by a computer',
          'A set of instructions that tells a computer what to do'
        ],
        'hardware': [
          'The physical parts of a computer system',
          'The machines, wiring, and other physical components of a computer',
          'Computer equipment, as opposed to the programs or instructions written for it'
        ]
      };

      const definitions = mockDefinitions[word.toLowerCase()] || [
        `No definition found for "${word}". This word is not available in the dictionary.`
      ];

      console.log(`🎯 "${word}" için ${definitions.length} tanım bulundu`);

      return {
        success: true,
        word: word,
        definitions: definitions,
        message: `Definitions for "${word}" retrieved successfully.`,
        source: 'Local Dictionary Database'
      };
    } catch (error: any) {
      console.error('❌ MCP tool error:', error);
      return {
        error: 'There was an issue with the MCP connection. Please try again later.',
        details: error instanceof Error ? error.message : 'Unknown error',
        word: word
      };
    }
  },
});
