import React, { useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { getWordDefinition } from './api/dictionaryApi';

export default function App() {
  const [word, setWord] = useState('');
  const [definition, setDefinition] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchedWord, setSearchedWord] = useState('');

  const handleSubmit = async () => {
    if (!word.trim()) {
      Alert.alert('Uyarı', 'Lütfen bir kelime girin.');
      return;
    }

    setLoading(true);
    setSearchedWord(word);
    setDefinition('');

    try {
      const result = await getWordDefinition(word.trim());
      setDefinition(result);
    } catch (error) {
      console.error('Error:', error);
      setDefinition('Bir hata oluştu. Lütfen tekrar deneyin.');
      Alert.alert('Hata', 'Bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setWord('');
    setDefinition('');
    setSearchedWord('');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor="#282c34" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>🤖 Mastra Dictionary Agent</Text>
            <Text style={styles.subtitle}>AI-powered word definitions using MCP tools</Text>
          </View>

          {/* Search Form */}
          <View style={styles.searchForm}>
            <TextInput
              style={styles.textInput}
              value={word}
              onChangeText={setWord}
              placeholder="Enter a word to define..."
              placeholderTextColor="#999"
              editable={!loading}
              onSubmitEditing={handleSubmit}
              returnKeyType="search"
            />

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.searchButton, (!word.trim() || loading) && styles.buttonDisabled]}
                onPress={handleSubmit}
                disabled={loading || !word.trim()}
              >
                <Text style={styles.buttonText}>
                  {loading ? '🔍 Searching...' : '🔍 Search'}
                </Text>
              </TouchableOpacity>

              {(word || definition) && (
                <TouchableOpacity
                  style={[styles.button, styles.clearButton, loading && styles.buttonDisabled]}
                  onPress={handleClear}
                  disabled={loading}
                >
                  <Text style={styles.buttonText}>✖ Clear</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Loading */}
          {loading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#61dafb" />
              <Text style={styles.loadingText}>Agent is thinking...</Text>
            </View>
          )}

          {/* Definition Result */}
          {definition && !loading && (
            <View style={styles.definitionContainer}>
              <Text style={styles.definitionTitle}>
                📖 Definition for: "{searchedWord}"
              </Text>
              <ScrollView style={styles.definitionScrollView}>
                <Text style={styles.definitionText}>{definition}</Text>
              </ScrollView>
            </View>
          )}

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>Powered by Mastra Agent Framework</Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#282c34',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#61dafb',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#ffffff',
    textAlign: 'center',
    opacity: 0.8,
  },
  searchForm: {
    marginBottom: 30,
  },
  textInput: {
    backgroundColor: '#ffffff',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    marginBottom: 15,
    borderWidth: 2,
    borderColor: '#61dafb',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  button: {
    flex: 1,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchButton: {
    backgroundColor: '#61dafb',
  },
  clearButton: {
    backgroundColor: '#ff6b6b',
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingContainer: {
    alignItems: 'center',
    marginVertical: 30,
  },
  loadingText: {
    color: '#ffffff',
    fontSize: 16,
    marginTop: 10,
  },
  definitionContainer: {
    backgroundColor: '#3a3f47',
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    borderWidth: 2,
    borderColor: '#61dafb',
  },
  definitionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#61dafb',
    marginBottom: 15,
    textAlign: 'center',
  },
  definitionScrollView: {
    maxHeight: 300,
  },
  definitionText: {
    fontSize: 16,
    color: '#ffffff',
    lineHeight: 24,
  },
  footer: {
    alignItems: 'center',
    marginTop: 30,
    paddingBottom: 20,
  },
  footerText: {
    color: '#ffffff',
    fontSize: 14,
    opacity: 0.7,
  },
});
