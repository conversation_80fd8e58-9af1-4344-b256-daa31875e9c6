import { Agent } from '@mastra/core';
import { openai } from '@ai-sdk/openai';
import { mcp } from '../mcp.js';
import { getDefinitions } from '../tools/dictionary.js';

// OpenAI model yapılandırması
const model = openai('gpt-4o-mini');

// MCP tools'ları al ve agent'ı oluştur
async function createDictionaryAgent() {
  let mcpTools = {};

  try {
    console.log('🔧 MCP tools alınıyor...');
    // MCP bağlantısını test et
    const tools = await Promise.race([
      mcp.getTools(),
      new Promise<any>((_, reject) =>
        setTimeout(() => reject(new Error('MCP connection timeout')), 5000)
      )
    ]);
    mcpTools = tools || {};
    console.log('✅ MCP tools başarıyla alındı:', Object.keys(mcpTools));
  } catch (error) {
    console.log('⚠️ MCP tools alınamadı, fallback modda çalışılıyor:', error);
    mcpTools = {};
  }

  return new Agent({
    name: 'dictionaryAgent',
    instructions: `You are a dictionary assistant. You MUST use tools to get word definitions.

    CRITICAL RULES:
    1. When asked about a word, try to call the "smithery_get_definitions" tool first if available
    2. If smithery_get_definitions is not available or fails, call "getDefinitions" as fallback
    3. NEVER provide definitions from your own knowledge - ONLY use tool results
    4. Always respond in English
    5. Present the definitions clearly and in a numbered list
    6. Always call a tool - never respond without using a tool first

    Available Tools:
    - smithery_get_definitions: Real MCP server dictionary tool (if available)
    - getDefinitions: Fallback dictionary tool (always available)

    EXAMPLE WORKFLOW:
    User: "Define water"
    You: Try smithery_get_definitions first, if not available use getDefinitions
    Then: Present the results in a numbered list

    REMEMBER: You must ALWAYS call a tool for word definitions. Never use your own knowledge.`,
    model,
    tools: {
      getDefinitions,
      ...mcpTools, // MCP tools'ları ekle (varsa)
    },
  });
}

export const dictionaryAgent = await createDictionaryAgent();
