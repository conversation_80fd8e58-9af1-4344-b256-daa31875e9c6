// API base URL - bu mobil uygulamada localhost yerine gerçek IP adresi kullanılmalı
// Expo Go ile test ederken bilgisayarınızın IP adresini kullanın
const API_BASE_URL = 'http://*************:3003'; // Bilgisayarın gerçek IP adresi

export async function getWordDefinition(word) {
  try {
    console.log(`🔍 Requesting definition for: ${word}`);

    const response = await fetch(`${API_BASE_URL}/api/dictionary`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ word }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || data.message || 'Unknown error');
    }

    console.log(`✅ Definition received for: ${word}`);
    return data.definition;

  } catch (error) {
    console.error("Error getting definition:", error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return `Error getting definition for "${word}": ${errorMessage}. Please try again.`;
  }
}
