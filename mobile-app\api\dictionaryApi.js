// API base URL - bu mobil uygulamada localhost yerine gerçek IP adresi kullanılmalı
// Expo Go ile test ederken bilgisayarınızın IP adresini kullanın
const API_BASE_URL = 'http://*************:3003'; // Bilgisayarın gerçek IP adresi

export async function getWordDefinition(word) {
  try {
    console.log(`🔍 Requesting definition for: ${word}`);

    const response = await fetch(`${API_BASE_URL}/api/dictionary`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ word }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Server'dan gelen response formatını kontrol et
    console.log('Server response:', data);

    if (data.success === false) {
      throw new Error(data.error || data.message || 'Unknown error');
    }

    console.log(`✅ Definition received for: ${word}`);
    // Server'dan gelen response formatına göre definition'ı al
    return data.definition || data.word || JSON.stringify(data);

  } catch (error) {
    console.error("Error getting definition:", error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return `Error getting definition for "${word}": ${errorMessage}. Please try again.`;
  }
}
